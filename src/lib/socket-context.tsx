'use client';

import useUserStore from '@/store/userStore';
import { createContext, useContext, useEffect, useState } from 'react';
import io from 'socket.io-client';

type SocketType = ReturnType<typeof io>;

interface SocketContextType {
  socket: SocketType | null;
  isConnected: boolean;
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
});

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

interface SocketProviderProps {
  children: React.ReactNode;
}

export const SocketProvider = ({ children }: SocketProviderProps) => {
  const [socket, setSocket] = useState<SocketType | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const { userData } = useUserStore();

  useEffect(() => {
    if (userData) {
      // const socketInstance = io(`http://localhost:8081?userId=${userData.id}`, {
      const socketUrl: any = process.env.NEXT_PUBLIC_SOCKET_URL;
      const socketInstance = io(`${socketUrl}?userId=${userData.id}`, {
        transports: ['websocket'],
        timeout: false, // Disable connection timeout
        pingTimeout: false, // Disable ping timeout
        pingInterval: false, // Disable ping interval
        forceNew: true, // Force a new connection
        reconnection: true, // Enable reconnection
        reconnectionAttempts: Infinity, // Infinite reconnection attempts
        reconnectionDelay: 1000, // 1 second delay between reconnection attempts
        reconnectionDelayMax: 5000, // Maximum delay between reconnection attempts
        maxReconnectionAttempts: Infinity, // Infinite reconnection attempts
      });

      socketInstance.on('connected', () => {
        console.log('Connected to socket server');
        setIsConnected(true);
      });

      socketInstance.on('disconnect', () => {
        console.log('Disconnected from socket server');
        setIsConnected(false);
      });

      socketInstance.on('connect_error', (error: Error) => {
        console.error('Socket connection error:', error);
        setIsConnected(false);
      });

      setSocket(socketInstance);

      return () => {
        socketInstance.disconnect();
      };
    }
  }, [userData]);

  return (
    <SocketContext.Provider value={{ socket, isConnected }}>{children}</SocketContext.Provider>
  );
};
